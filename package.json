{"name": "conative-payment-service", "version": "0.0.0", "description": "", "scripts": {"build": "node build.js && tsc --build tsconfig.json && resolve-tspaths", "debug": "nodemon --env=development --config nodemon.debug.json", "bdev": "node build.js && tsc --build tsconfig.json", "start": "node dist/index.js", "start:dev": "nodemon --env=development --config nodemon.debug.json", "nodemon": "nodemon --env=development --config nodemon.json", "dev": "nodemon", "migrate:make": "knex migrate:make", "migrate:up": "knex migrate:up", "migrate:latest": "npx knex migrate:latest --env=production", "migrate:down": "knex migrate:down", "migrate:rollback": "knex migrate:rollback", "migrate:list": "knex migrate:list", "seed:make": "knex seed:make", "seed:run": "knex seed:run", "test": "jest --updateSnapshot --detectOpenHandles --passWithNoTests", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --fix --ext .js,.ts .", "lint:format": "prettier --write ."}, "keywords": [], "nodemonConfig": {"legacyWatch": true, "watch": ["src"], "ext": "ts,html", "ignore": ["src/public"], "exec": "ts-node -r tsconfig-paths/register ./src"}, "dependencies": {"@cbidigital/aqua-ddd": "^1.0.0-rc.9", "@cbidigital/membership-module": "^1.0.0-alpha.9", "@cbidigital/payment-module": "^2.0.0-alpha.26", "@heronjs/common": "3.4.10", "@heronjs/core": "3.5.10", "@heronjs/ebs": "^1.0.15", "@heronjs/express": "3.1.16", "axios": "^0.27.2", "express": "^4.17.1", "http-status-codes": "^2.2.0", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "pg": "^8.16.0", "reflect-metadata": "^0.1.13", "resolve-tspaths": "^0.8.0", "stripe": "^18.2.1", "ts-node": "^10.9.1", "tslib": "^2.4.0", "zod": "^3.25.51"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/express": "^4.17.13", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^18.7.18", "class-validator": "^0.13.1", "eslint": "^9.28.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "globals": "^16.2.0", "nodemon": "^2.0.7", "prettier": "^2.4.1", "tsconfig-paths": "^3.9.0", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1"}}