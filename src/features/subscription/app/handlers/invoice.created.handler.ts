import {
    IData<PERSON><PERSON><PERSON>,
    InvoiceCreatedEvent,
    ITransactionBuilder,
    IWebhookEventBuilder,
    ITransactionRepository,
    IWebhookEventRepository,
    PAYMENT_MODULE_INJECT_TOKENS,
} from '@cbidigital/payment-module';
import {
    ISubscriptionRepository,
    MEMBERSHIP_INJECT_TOKENS,
    SubscriptionNotFoundError,
    MissingSubscriptionIdError,
} from '@cbidigital/membership-module';
import { IWebhookHandler } from './webhook.handler';
import { PAYMENT_SERVICE_INJECT_TOKENS } from '@shared';
import { RepositoryOptions } from '@cbidigital/aqua-ddd';
import { ILogger, Inject, Lifecycle, Logger, Provider } from '@heronjs/common';

@Provider({
    token: PAYMENT_SERVICE_INJECT_TOKENS.HANDLER.INVOICE_CREATED,
    scope: Lifecycle.Singleton,
})
export class InvoiceCreated<PERSON><PERSON><PERSON> implements IWebhookHandler {
    private readonly logger: ILogger;

    constructor(
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.BUILDER.WEBHOOK_EVENT)
        protected readonly webhookEventBuilder: IWebhookEventBuilder,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.WEBHOOK_EVENT)
        protected readonly webhookEventRepo: IWebhookEventRepository,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.TRANSACTION)
        protected readonly transactionRepo: ITransactionRepository,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.BUILDER.TRANSACTION)
        protected readonly builder: ITransactionBuilder,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.UTIL.DATABASE)
        protected readonly databaseUtil: IDatabaseUtil,
        @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.SUBSCRIPTION)
        protected readonly subsRepo: ISubscriptionRepository,
    ) {
        this.logger = new Logger(this.constructor.name);
    }

    async handle(event: InvoiceCreatedEvent) {
        const trx = await this.databaseUtil.startTrx();
        const repoOptions = { trx };
        try {
            const isHandled = await this.isWebhookEventHandled(event, repoOptions);
            if (isHandled) return;
            await Promise.all([
                this.createTransaction(event, repoOptions),
                this.addInvoice(event, repoOptions),
                this.saveWebhookEvent(event, repoOptions),
            ]);
            await trx.commit();
        } catch (error) {
            await trx.rollback();
            this.logger.error('Failed to handle invoice created', error);
            throw error;
        }
    }

    private async isWebhookEventHandled(event: InvoiceCreatedEvent, repoOptions: RepositoryOptions) {
        return this.webhookEventRepo.findOne(
            {
                filter: { eventId: { $eq: event.eventId }, gateway: { $eq: event.gateway } },
            },
            repoOptions,
        );
    }

    private async saveWebhookEvent(event: InvoiceCreatedEvent, repoOptions: RepositoryOptions) {
        const webhookEvent = await this.webhookEventBuilder.build();
        await webhookEvent.create({
            eventId: event.eventId,
            gateway: event.gateway,
            eventType: event.type,
            payload: event,
            referenceId: event.data.invoice,
        });
        await this.webhookEventRepo.create(webhookEvent, repoOptions);
    }

    private async createTransaction(event: InvoiceCreatedEvent, repoOptions: RepositoryOptions) {
        const existingTransaction = await this.transactionRepo.findOne(
            {
                filter: {
                    gatewayTransactionId: { $eq: event.data.invoice },
                    gateway: { $eq: event.gateway },
                },
            },
            repoOptions,
        );

        if (existingTransaction) {
            this.logger.info(
                `Transaction for invoice ${event.data.invoice} already exists, skipping creation`,
            );
            return;
        }

        // Create new transaction
        const transaction = await this.builder.build();
        await transaction.create({
            gateway: event.gateway,
            gatewayCustomerId: event.data.customer,
            gatewayTransactionId: event.data.invoice,
        });
        await this.transactionRepo.create(transaction, repoOptions);
        this.logger.info(`Transaction created for invoice ${event.data.invoice}`);
    }

    private async addInvoice(event: InvoiceCreatedEvent, repoOptions: RepositoryOptions) {
        const { data } = event;
        const { appSubscriptionId } = data.metadata;
        if (!appSubscriptionId) throw new MissingSubscriptionIdError();
        const subscription = await this.subsRepo.findOne(
            { filter: { id: { $eq: appSubscriptionId } } },
            repoOptions,
        );
        if (!subscription) throw new SubscriptionNotFoundError();

        // Check if invoice already exists (could be created by payment-succeeded handler)
        const existingInvoice = subscription.getGatewayInvoice(event.data.invoice, event.gateway);
        if (existingInvoice) {
            this.logger.info(
                `Invoice ${event.data.invoice} already exists for subscription ${appSubscriptionId}, skipping creation`,
            );
            return;
        }

        // Add invoice to subscription
        await subscription.addInvoice({ gatewayInvoiceId: event.data.invoice, gateway: event.gateway });
        await this.subsRepo.update(subscription, repoOptions);
        this.logger.info(`Invoice ${event.data.invoice} added to subscription ${appSubscriptionId}`);
    }
}
