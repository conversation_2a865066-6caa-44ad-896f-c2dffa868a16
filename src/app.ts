import { AuthContext } from '@context';
import { PostgresDataSource } from './data-source';
import { DataSources, GateKeeper, Module } from '@heronjs/common';
import { HandleWebhookEventUseCase } from '@features/subscription/app';
import { PaymentModule } from '@cbidigital/payment-module/src/features/payment/payment.module';
import { MembershipModule } from '@cbidigital/membership-module/src/features/membership.module';
import { AdminMembershipRest, PaymentWebhook, SubscriptionRest } from '@features/subscription/presentations';
import {
    PaymentFailedHandler,
    InvoiceCreatedHandler,
    WebhookHandlerFactory,
    PaymentSucceededHandler,
    SubscriptionCancelledHandler,
} from '@features/subscription/app/handlers';

@Module({
    controllers: [AdminMembershipRest, SubscriptionRest, PaymentWebhook],
    imports: [MembershipModule, PaymentModule],
    providers: [
        // usecases
        HandleWebhookEventUseCase,

        // handlers
        InvoiceCreated<PERSON><PERSON><PERSON>,
        PaymentFailedHandler,
        PaymentSucceededHandler,
        SubscriptionCancelledHandler,

        //factories
        WebhookHandlerFactory,
    ],
    services: [],
})
@GateKeeper(AuthContext, AuthContext.Resolver)
@DataSources([PostgresDataSource])
export class AppModule {}
